<?php
require_once 'db.php';
require_once 'functions.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['user_id'])) {
    $_SESSION['page_start_time'] = time();

    $userId = $_SESSION['user_id'];
    $page = basename($_SERVER['PHP_SELF']);
    $action = 'زيارة الصفحة';
    $url = $_SERVER['REQUEST_URI'];
    $method = $_SERVER['REQUEST_METHOD'];
    $data = ($method === 'POST') ? json_encode($_POST) : json_encode($_GET);

    // سجل الزيارة بدون مدة الآن
    logActivity($conn, $userId, $page, $action, $url, $method, $data, null);
}

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

$currentMonth = isset($_GET['month']) ? (int)$_GET['month'] : (int)date('m');
$currentYear = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');
$statusFilter = $_GET['status'] ?? '';

$startDate = "$currentYear-" . str_pad($currentMonth, 2, '0', STR_PAD_LEFT) . "-01";
$endDate = date("Y-m-t", strtotime($startDate));

// إضافة تنبيه (مبسط)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_notification']) && $isAdmin) {
    $title = trim($_POST['title']);
    $message = trim($_POST['message']);
    $notifyDate = $_POST['notify_date'];
    $toDate = $_POST['end_date'] ?? $_POST['notify_date'];

    // التحقق من صحة البيانات
    if (!empty($title) && !empty($message) && !empty($notifyDate) && !empty($toDate)) {
        // التحقق من أن تاريخ النهاية لا يسبق تاريخ البداية
        if (strtotime($toDate) >= strtotime($notifyDate)) {
            $stmt = $conn->prepare("INSERT INTO Notifications (Title, Message, NotifyDate, ToDate, Status) VALUES (?, ?, ?, ?, 'لم تنفذ')");
            $stmt->execute([$title, $message, $notifyDate, $toDate]);
            $_SESSION['success'] = "تمت إضافة المهمة بنجاح.";
        } else {
            $_SESSION['error'] = "تاريخ النهاية يجب أن يكون بعد أو يساوي تاريخ البداية.";
        }
    } else {
        $_SESSION['error'] = "يرجى ملء جميع الحقول المطلوبة.";
    }
    header("Location: Add_Notification2.php");
    exit;
}

// حذف تنبيه
if (isset($_GET['delete']) && $isAdmin) {
    $taskId = filter_var($_GET['delete'], FILTER_VALIDATE_INT);

    if ($taskId) {
        $stmt = $conn->prepare("DELETE FROM Notifications WHERE ID = ?");
        $stmt->execute([$taskId]);
        $_SESSION['success'] = "تم حذف المهمة بنجاح.";
    } else {
        $_SESSION['error'] = "خطأ في معرف المهمة.";
    }
    header("Location: Add_Notification2.php");
    exit;
}

// تحديث الحالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status']) && $isAdmin) {
    $taskId = filter_var($_POST['id'], FILTER_VALIDATE_INT);
    $newStatus = trim($_POST['status']);

    // التحقق من صحة البيانات
    $allowedStatuses = ['تم', 'تحت التنفيذ', 'لم تنفذ'];
    if ($taskId && in_array($newStatus, $allowedStatuses)) {
        $stmt = $conn->prepare("UPDATE Notifications SET Status = ? WHERE ID = ?");
        $stmt->execute([$newStatus, $taskId]);
        $_SESSION['success'] = "تم تحديث الحالة بنجاح.";
    } else {
        $_SESSION['error'] = "خطأ في البيانات المرسلة.";
    }
    header("Location: Add_Notification2.php?month=$currentMonth&year=$currentYear");
    exit;
}

// ترحيل المهمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reschedule']) && $isAdmin) {
    $taskId = filter_var($_POST['reschedule'], FILTER_VALIDATE_INT);

    if ($taskId) {
        $stmt = $conn->prepare("UPDATE Notifications SET NotifyDate = DATE_ADD(NotifyDate, INTERVAL 1 DAY), ToDate = DATE_ADD(ToDate, INTERVAL 1 DAY) WHERE ID = ?");
        $stmt->execute([$taskId]);
        $_SESSION['success'] = "تم ترحيل المهمة ليوم الغد بنجاح.";
    } else {
        $_SESSION['error'] = "خطأ في معرف المهمة.";
    }
    header("Location: Add_Notification2.php?month=$currentMonth&year=$currentYear");
    exit;
}

// إضافة متغيرات البحث والفلترة المحسنة
$searchQuery = $_GET['search'] ?? '';
$priorityFilter = $_GET['priority'] ?? '';
$categoryFilter = $_GET['category'] ?? '';
$assignedToFilter = $_GET['assigned_to'] ?? '';
$progressFilter = $_GET['progress'] ?? '';
$dateFromFilter = $_GET['date_from'] ?? '';
$dateToFilter = $_GET['date_to'] ?? '';
$createdByFilter = $_GET['created_by'] ?? '';
$sortBy = $_GET['sort_by'] ?? 'NotifyDate';
$sortOrder = $_GET['sort_order'] ?? 'ASC';

// استعلام محسن مع حسابات SQL
$query = "SELECT
    ID, Title, Message, NotifyDate, ToDate, Status,
    CASE
        WHEN Status = 'تم' THEN 'completed'
        WHEN ToDate < CURDATE() AND Status != 'تم' THEN 'overdue'
        WHEN NotifyDate = CURDATE() THEN 'today'
        WHEN ToDate = CURDATE() THEN 'end_today'
        ELSE 'normal'
    END as row_class,
    CASE
        WHEN Status = 'تم' THEN 'status-completed'
        WHEN ToDate < CURDATE() AND Status != 'تم' THEN 'status-overdue'
        WHEN Status = 'تحت التنفيذ' THEN 'status-in-progress'
        ELSE 'status-not-started'
    END as status_class,
    CASE
        WHEN Status = 'تم' THEN 'fas fa-check-circle'
        WHEN ToDate < CURDATE() AND Status != 'تم' THEN 'fas fa-exclamation-triangle'
        WHEN Status = 'تحت التنفيذ' THEN 'fas fa-clock'
        ELSE 'fas fa-hourglass-half'
    END as status_icon,
    CASE
        WHEN Status = 'تم' THEN 'مكتمل'
        WHEN ToDate < CURDATE() AND Status != 'تم' THEN CONCAT('متأخر ', DATEDIFF(CURDATE(), ToDate), ' يوم')
        WHEN ToDate = CURDATE() THEN 'ينتهي اليوم'
        WHEN ToDate > CURDATE() THEN CONCAT(DATEDIFF(ToDate, CURDATE()), ' يوم متبقي')
        ELSE ''
    END as days_remaining
FROM Notifications
WHERE (NotifyDate BETWEEN ? AND ? OR ToDate BETWEEN ? AND ?)";

$params = [$startDate, $endDate, $startDate, $endDate];

// إضافة فلتر البحث
if ($searchQuery !== '') {
    $searchQuery = htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8');
    $query .= " AND (Title LIKE ? OR Message LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

// إضافة فلتر الحالة
if ($statusFilter !== '') {
    $query .= " AND Status = ?";
    $params[] = $statusFilter;
}

// إضافة فلتر التاريخ المخصص
if ($dateFromFilter !== '') {
    $query .= " AND NotifyDate >= ?";
    $params[] = $dateFromFilter;
}
if ($dateToFilter !== '') {
    $query .= " AND ToDate <= ?";
    $params[] = $dateToFilter;
}

// إضافة الترتيب
$allowedSortColumns = ['NotifyDate', 'ToDate', 'Title', 'Status'];
$allowedSortOrders = ['ASC', 'DESC'];

if (in_array($sortBy, $allowedSortColumns) && in_array($sortOrder, $allowedSortOrders)) {
    $query .= " ORDER BY $sortBy $sortOrder";
} else {
    $query .= " ORDER BY NotifyDate ASC";
}

// إضافة LIMIT للتحميل التدريجي
$limit = 50; // عدد المهام في الصفحة الواحدة
$currentPage = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$currentPage = max(1, $currentPage); // التأكد من أن الصفحة لا تقل عن 1
$offset = ($currentPage - 1) * $limit;
$query .= " LIMIT $limit OFFSET $offset";

try {
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("خطأ في استعلام المهام: " . $e->getMessage());
    $notifications = [];
    $_SESSION['error'] = "حدث خطأ في تحميل المهام. يرجى المحاولة مرة أخرى.";
}

// حساب العدد الإجمالي للصفحات
$countQuery = "SELECT COUNT(*) as total FROM Notifications WHERE (NotifyDate BETWEEN ? AND ? OR ToDate BETWEEN ? AND ?)";
$countParams = [$startDate, $endDate, $startDate, $endDate];

if ($searchQuery !== '') {
    $countQuery .= " AND (Title LIKE ? OR Message LIKE ?)";
    $countParams[] = "%$searchQuery%";
    $countParams[] = "%$searchQuery%";
}
if ($statusFilter !== '') {
    $countQuery .= " AND Status = ?";
    $countParams[] = $statusFilter;
}
if ($dateFromFilter !== '') {
    $countQuery .= " AND NotifyDate >= ?";
    $countParams[] = $dateFromFilter;
}
if ($dateToFilter !== '') {
    $countQuery .= " AND ToDate <= ?";
    $countParams[] = $dateToFilter;
}

try {
    $countStmt = $conn->prepare($countQuery);
    $countStmt->execute($countParams);
    $totalTasks = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalTasks / $limit);
} catch (PDOException $e) {
    error_log("خطأ في حساب عدد المهام: " . $e->getMessage());
    $totalTasks = 0;
    $totalPages = 1;
}

// التأكد من أن الصفحة الحالية لا تتجاوز العدد الإجمالي للصفحات
if ($currentPage > $totalPages && $totalPages > 0) {
    $currentPage = $totalPages;
}

// حساب إحصائيات محسنة وسريعة باستخدام استعلام واحد
$stats = [
    'total' => 0,
    'completed' => 0,
    'in_progress' => 0,
    'pending' => 0,
    'overdue' => 0,
    'today' => 0,
    'completion_rate' => 0
];

// استعلام محسن للإحصائيات
try {
    $statsStmt = $conn->prepare("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN Status = 'لم تنفذ' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN ToDate < CURDATE() AND Status != 'تم' THEN 1 ELSE 0 END) as overdue,
            SUM(CASE WHEN NotifyDate = CURDATE() THEN 1 ELSE 0 END) as today
        FROM Notifications
        WHERE (NotifyDate BETWEEN ? AND ? OR ToDate BETWEEN ? AND ?)
    ");
    $statsStmt->execute([$startDate, $endDate, $startDate, $endDate]);
    $result = $statsStmt->fetch(PDO::FETCH_ASSOC);

    if ($result) {
        $stats = array_merge($stats, $result);
        // حساب معدل الإنجاز
        if ($stats['total'] > 0) {
            $stats['completion_rate'] = round(($stats['completed'] / $stats['total']) * 100, 1);
        }
    }

} catch (PDOException $e) {
    // قيم افتراضية في حالة الخطأ
    error_log("خطأ في حساب الإحصائيات: " . $e->getMessage());
}

// جلب المستخدمين (مبسط)
$users = [];
try {
    $usersStmt = $conn->prepare("SELECT ID, Username FROM users ORDER BY Username LIMIT 50");
    $usersStmt->execute();
    $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// تصنيفات افتراضية بسيطة
$categories = [
    ['Name' => 'عام'],
    ['Name' => 'مالية'],
    ['Name' => 'إدارية'],
    ['Name' => 'تقنية'],
    ['Name' => 'عاجل']
];

$prevMonth = $currentMonth - 1; $prevYear = $currentYear;
$nextMonth = $currentMonth + 1; $nextYear = $currentYear;
if ($prevMonth < 1) { $prevMonth = 12; $prevYear--; }
if ($nextMonth > 12) { $nextMonth = 1; $nextYear++; }
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة المهام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="notifications-style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- تحسينات الأداء المتقدمة -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">

    <!-- Meta tags للأداء -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- تحسين التخزين المؤقت -->
    <meta http-equiv="Cache-Control" content="public, max-age=3600">
    <meta http-equiv="Expires" content="<?= date('D, d M Y H:i:s', time() + 3600) ?> GMT">
    <style>
        /* تحسينات خاصة لصفحة إدارة المهام - تصميم محسن مع تحسينات الأداء */
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            /* تحسينات الأداء */
            will-change: scroll-position;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* تحسين التحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: #007bff;
            font-size: 16px;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسينات الصفحات */
        .pagination-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .pagination .page-link {
            color: #007bff;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background-color: #e9ecef;
            border-color: #007bff;
            transform: translateY(-1px);
        }

        .pagination .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
            font-weight: bold;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 0.9em;
        }

        /* تحسينات الأداء للجدول */
        .table-responsive {
            will-change: scroll-position;
            -webkit-overflow-scrolling: touch;
        }

        .task-row {
            transition: background-color 0.2s ease;
        }

        .task-row:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* تحسين عرض الحالات */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين الأنيميشن والتفاعل */
        * {
            box-sizing: border-box;
        }

        .smooth-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* تحسين الأداء للجداول الكبيرة */
        .table-container {
            contain: layout style paint;
            will-change: scroll-position;
        }

        .virtual-scroll {
            height: 400px;
            overflow-y: auto;
            contain: strict;
        }

        /* تحسين التمرير */
        .smooth-scroll {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        /* تحسين الاستجابة */
        .responsive-text {
            font-size: clamp(0.875rem, 2.5vw, 1rem);
        }

        /* تحسين التفاعل مع اللمس */
        .touch-friendly {
            min-height: 44px;
            min-width: 44px;
            touch-action: manipulation;
        }

        /* تحسين الأزرار للأجهزة اللمسية */
        .btn {
            touch-action: manipulation;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* تحسين النماذج */
        .form-control,
        .form-select {
            will-change: border-color, box-shadow;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        /* تحسين الصور والأيقونات */
        .fas,
        .far {
            will-change: transform;
        }

        /* تحسين الجدول للأداء */
        .table {
            contain: layout style;
        }

        .table tbody tr {
            contain: layout style paint;
        }

        /* تحسين الذاكرة للعناصر المخفية */
        .hidden-element {
            display: none !important;
            visibility: hidden;
            opacity: 0;
        }

        /* تحسين الرسوم المتحركة */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* تحسين الطباعة المتقدم */
        @media print {
            .no-print,
            .sidebar,
            .header,
            .search-filter-section,
            .filter-actions,
            .action-buttons,
            .btn,
            .modal-overlay {
                display: none !important;
            }

            .print-break {
                page-break-before: always;
            }

            .print-avoid-break {
                page-break-inside: avoid;
            }

            body {
                background: white !important;
                color: black !important;
                font-size: 12pt;
                line-height: 1.4;
            }

            .notifications-table {
                box-shadow: none !important;
                border: 1px solid #000 !important;
            }

            .notifications-table .table th {
                background: #f0f0f0 !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }

            .notifications-table .table td {
                border: 1px solid #000 !important;
                padding: 8px !important;
            }

            .page-header {
                background: #f0f0f0 !important;
                color: #000 !important;
                border: 1px solid #000 !important;
                margin-bottom: 20px;
            }

            .stats-dashboard {
                display: none !important;
            }

            .detailed-stats {
                page-break-before: always;
            }

            /* تحسين عرض الجدول للطباعة */
            .table {
                font-size: 10pt !important;
            }

            .table th,
            .table td {
                padding: 4px !important;
                font-size: 9pt !important;
            }

            /* إخفاء العناصر غير الضرورية */
            .task-description {
                max-height: none !important;
                overflow: visible !important;
            }

            .priority-badge,
            .status-badge {
                border: 1px solid #000 !important;
                background: white !important;
                color: black !important;
            }
        }

        /* تحسين التصدير */
        .export-options {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        .export-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .export-btn:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        .export-btn.excel {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .export-btn.csv {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .export-btn.pdf {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .export-btn.print {
            background: linear-gradient(135deg, #6c757d, #5a6268);
        }

        .notifications-container {
            max-width: 1400px;
            margin: auto;
            margin-top: 20px;
            padding: 0 15px;
        }

        /* لوحة الإحصائيات الجديدة */
        .stats-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border: 2px solid #007bff;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
        }

        .stat-card .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .stat-card.completed .stat-icon { color: #28a745; }
        .stat-card.in-progress .stat-icon { color: #007bff; }
        .stat-card.pending .stat-icon { color: #ffc107; }
        .stat-card.overdue .stat-icon { color: #dc3545; }

        .stat-card .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
        }

        .stat-card .stat-label {
            font-size: 1.1rem;
            color: #495057;
            font-weight: 600;
        }

        .stat-card .stat-percentage {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
            display: block;
        }

        .stat-card {
            cursor: pointer;
        }

        /* لوحة الإحصائيات التفصيلية */
        .detailed-stats {
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
            height: 100%;
        }

        .stats-card h6 {
            color: #000000;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .category-item,
        .user-item {
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.3s ease;
        }

        .category-item:hover,
        .user-item:hover {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding-left: 10px;
            padding-right: 10px;
        }

        .category-item:last-child,
        .user-item:last-child {
            border-bottom: none;
        }

        /* تحسين شريط التقدم */
        .progress {
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            transition: width 0.6s ease;
        }

        /* رسم بياني دائري للإحصائيات */
        .chart-container {
            position: relative;
            height: 200px;
            margin: 20px 0;
        }

        .chart-legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        /* تحسين الاستجابة للإحصائيات */
        @media (max-width: 768px) {
            .stats-dashboard {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stat-card {
                padding: 15px;
            }

            .stat-card .stat-icon {
                font-size: 2rem;
                margin-bottom: 10px;
            }

            .stat-card .stat-number {
                font-size: 1.8rem;
                margin-bottom: 5px;
            }

            .stat-card .stat-label {
                font-size: 0.9rem;
            }

            .detailed-stats .col-md-6 {
                margin-bottom: 20px;
            }
        }

        /* تحسين البحث والفلترة */
        .search-filter-section {
            background: white;
            border: 2px solid #007bff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
        }

        .search-filter-section h5 {
            color: #000000;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #007bff;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            border-color: #000000;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            transform: translateY(-2px);
        }

        .search-box .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #007bff;
            font-size: 1.2rem;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group label {
            display: block;
            margin-bottom: 8px;
            color: #000000;
            font-weight: 600;
        }

        .filter-group select,
        .filter-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            border-color: #000000;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn-filter {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-filter:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
        }

        .btn-clear {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-clear:hover {
            background: linear-gradient(135deg, #5a6268, #6c757d);
            transform: translateY(-2px);
        }

        .page-header {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            padding: 25px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .page-header h4 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .page-header .subtitle {
            margin-top: 8px;
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: none;
        }

        .card-body-enhanced {
            padding: 30px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        /* تحسين نموذج إضافة التنبيه */
        .add-notification-form {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #007bff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
        }

        .add-notification-form .form-label {
            color: #000000;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .add-notification-form .form-control,
        .add-notification-form .form-select {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background: white;
        }

        .add-notification-form .form-control:focus,
        .add-notification-form .form-select:focus {
            border-color: #000000;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            transform: translateY(-2px);
        }

        /* تحسين أزرار الإضافة */
        .btn-add-notification {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn-add-notification:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        /* تحسين التنقل بين الشهور */
        .month-navigation {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .month-navigation h5 {
            margin: 0;
            color: #000000;
            font-weight: 600;
            font-size: 1.3rem;
        }

        .nav-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            color: white;
        }

        /* تحسين فلتر الحالة */
        .status-filter {
            background: white;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .status-filter .form-select {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
            background: white;
        }

        /* تحسين الجدول */
        .notifications-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
        }

        .notifications-table .table {
            margin: 0;
            width: 100%;
        }

        .notifications-table .table th {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 18px 15px;
            border: none;
            font-size: 0.95rem;
        }

        .notifications-table .table td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.95rem;
        }

        .notifications-table .table tbody tr {
            transition: all 0.3s ease;
        }

        .notifications-table .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* تحسين تمييز الصفوف */
        .highlight-today {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
            font-weight: bold;
            border-left: 4px solid #ffc107;
        }

        .highlight-end-today {
            background: linear-gradient(135deg, #d1e7dd, #a8e6cf) !important;
            border-left: 4px solid #28a745;
        }

        /* تحسين أزرار الإجراءات */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .action-buttons .form-select {
            border: 2px solid #007bff;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 0.85rem;
            min-width: 150px;
        }

        .action-buttons .btn {
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            min-width: 150px;
            transition: all 0.3s ease;
        }

        .btn-update-status {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
        }

        .btn-update-status:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
        }

        .btn-reschedule {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            border: none;
        }

        .btn-reschedule:hover {
            background: linear-gradient(135deg, #5a6268, #6c757d);
            transform: translateY(-2px);
        }

        .btn-edit {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
        }

        .btn-delete {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            border: none;
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #333333, #000000);
            transform: translateY(-2px);
        }

        /* تحسين رسائل التنبيه */
        .alert-success {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #007bff;
            border-radius: 10px;
            color: #0d47a1;
            padding: 15px 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
        }

        /* تحسين الاستجابة للأجهزة المحمولة */
        @media (max-width: 768px) {
            .notifications-container {
                padding: 0 10px;
                margin-top: 20px;
            }

            .add-notification-form {
                padding: 20px;
            }

            .month-navigation {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .action-buttons {
                gap: 5px;
            }

            .action-buttons .btn,
            .action-buttons .form-select {
                min-width: 120px;
                font-size: 0.8rem;
                padding: 6px 10px;
            }

            .notifications-table .table th,
            .notifications-table .table td {
                padding: 10px 8px;
                font-size: 0.85rem;
            }
        }

        /* تحسين حالات المهام مع إضافة المزيد من الحالات */
        .status-badge {
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .status-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .status-completed {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 2px solid #28a745;
        }

        .status-in-progress {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #0d47a1;
            border: 2px solid #007bff;
        }

        .status-not-started {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 2px solid #ffc107;
        }

        .status-overdue {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 2px solid #dc3545;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }

        /* تحسين أزرار الأولوية */
        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .priority-high {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .priority-medium {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .priority-low {
            background: linear-gradient(135deg, #28a745, #218838);
            color: white;
        }

        /* تحسين الجدول مع إضافة المزيد من الميزات */
        .task-row {
            transition: all 0.3s ease;
            position: relative;
        }

        .task-row:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
            transform: translateX(5px);
        }

        .task-row.completed {
            opacity: 0.8;
            background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        }

        .task-row.overdue {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb) !important;
            border-left: 4px solid #dc3545;
        }

        .task-title {
            font-weight: 600;
            color: #212529;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .task-title:hover {
            color: #007bff;
            text-decoration: underline;
        }

        .task-description {
            max-width: 300px;
            max-height: 100px;
            overflow-y: auto;
            text-align: right;
            line-height: 1.5;
            padding: 10px;
            background: rgba(248, 249, 250, 0.5);
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .task-dates {
            display: flex;
            flex-direction: column;
            gap: 5px;
            align-items: center;
        }

        .date-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #0d47a1;
            border: 1px solid #007bff;
        }

        /* تحسين أزرار الإجراءات */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
            min-width: 180px;
        }

        .action-buttons .btn {
            min-width: 160px;
            padding: 8px 12px;
            font-size: 0.85rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-buttons .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .action-buttons .btn:hover::before {
            left: 100%;
        }

        /* تحسين النماذج المنبثقة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>
</head>
<body>

<!-- شاشة التحميل المحسنة -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="text-center">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري تحميل المهام...</div>
        <p class="mt-3">جاري تحميل المهام...</p>
    </div>
</div>

<?php include 'header.php'; ?>

<div class="main-content" id="mainContent">
    <div class="main-card">
        <div class="page-header">
            <h4><i class="fas fa-tasks"></i> إدارة المهام والتنبيهات</h4>
            <div class="subtitle">نظام إدارة وتتبع المهام اليومية</div>
        </div>
        <div class="card-body-enhanced">

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success text-center"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger text-center"><?= $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <!-- لوحة الإحصائيات البسيطة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?= $stats['completed'] ?></h5>
                            <p class="card-text">مهام مكتملة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info"><?= $stats['in_progress'] ?></h5>
                            <p class="card-text">قيد التنفيذ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning"><?= $stats['pending'] ?></h5>
                            <p class="card-text">في الانتظار</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?= $stats['total'] ?></h5>
                            <p class="card-text">إجمالي المهام</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم البحث والفلترة البسيط -->
            <div class="search-filter-section">
                <h5><i class="fas fa-search"></i> البحث والفلترة</h5>
                <form method="GET" id="searchForm">
                    <input type="hidden" name="month" value="<?= $currentMonth ?>">
                    <input type="hidden" name="year" value="<?= $currentYear ?>">

                    <div class="row g-3">
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control" placeholder="ابحث في العنوان أو الوصف..."
                                   value="<?= htmlspecialchars($searchQuery) ?>" id="searchInput">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="تم" <?= $statusFilter == 'تم' ? 'selected' : '' ?>>✅ مكتملة</option>
                                <option value="تحت التنفيذ" <?= $statusFilter == 'تحت التنفيذ' ? 'selected' : '' ?>>🔄 قيد التنفيذ</option>
                                <option value="لم تنفذ" <?= $statusFilter == 'لم تنفذ' ? 'selected' : '' ?>>⏳ في الانتظار</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" name="date_from" class="form-control" value="<?= htmlspecialchars($dateFromFilter) ?>" placeholder="من تاريخ">
                        </div>
                        <div class="col-md-2">
                            <input type="date" name="date_to" class="form-control" value="<?= htmlspecialchars($dateToFilter) ?>" placeholder="إلى تاريخ">
                        </div>
                        <div class="col-md-1">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> مسح الفلاتر
                        </button>
                        <a href="export_tasks.php?type=excel" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </a>
                    </div>
                </form>
            </div>

            <?php if ($isAdmin): ?>
            <div class="add-notification-form">
                <h5 class="text-center mb-4" style="color: #000000; font-weight: 600;">
                    <i class="fas fa-plus-circle"></i> إضافة مهمة جديدة
                </h5>
                <form method="POST" class="row g-3">
                    <input type="hidden" name="add_notification" value="1">
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-heading"></i> عنوان المهمة</label>
                        <input type="text" name="title" class="form-control" placeholder="أدخل عنوان المهمة..." required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label"><i class="fas fa-calendar-alt"></i> من تاريخ</label>
                        <input type="date" name="notify_date" class="form-control" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label"><i class="fas fa-calendar-check"></i> إلى تاريخ</label>
                        <input type="date" name="end_date" class="form-control" required>
                    </div>
                    <div class="col-md-12">
                        <label class="form-label"><i class="fas fa-align-left"></i> وصف المهمة</label>
                        <textarea name="message" class="form-control" rows="4" placeholder="أدخل تفاصيل المهمة..." required></textarea>
                    </div>
                    <div class="col-md-12 text-center">
                        <button type="submit" class="btn-add-notification">
                            <i class="fas fa-plus"></i> إضافة مهمة جديدة
                        </button>
                    </div>
                </form>
            </div>
            <?php endif; ?>

            <!-- تنقل بين الشهور -->
            <div class="month-navigation">
                <a href="?month=<?= $prevMonth ?>&year=<?= $prevYear ?>" class="nav-btn" title="الشهر السابق">
                    <i class="fas fa-chevron-right"></i> الشهر السابق
                </a>
                <h5><i class="fas fa-calendar"></i> مهام شهر <?= date("F Y", strtotime($startDate)) ?></h5>
                <a href="?month=<?= $nextMonth ?>&year=<?= $nextYear ?>" class="nav-btn" title="الشهر التالي">
                    الشهر التالي <i class="fas fa-chevron-left"></i>
                </a>
            </div>

            <!-- فلتر حسب الحالة -->
            <div class="status-filter">
                <form method="GET" class="d-flex align-items-center gap-3">
                    <input type="hidden" name="month" value="<?= $currentMonth ?>">
                    <input type="hidden" name="year" value="<?= $currentYear ?>">
                    <label class="form-label mb-0" style="color: #000000; font-weight: 600;">
                        <i class="fas fa-filter"></i> فلترة حسب الحالة:
                    </label>
                    <select name="status" class="form-select w-auto" onchange="this.form.submit()">
                        <option value="">🔍 جميع الحالات</option>
                        <option value="تم" <?= $statusFilter == 'تم' ? 'selected' : '' ?>>✅ تم إنجازها</option>
                        <option value="تحت التنفيذ" <?= $statusFilter == 'تحت التنفيذ' ? 'selected' : '' ?>>🔄 تحت التنفيذ</option>
                        <option value="لم تنفذ" <?= $statusFilter == 'لم تنفذ' ? 'selected' : '' ?>>❌ لم تنفذ بعد</option>
                    </select>
                </form>
            </div>

            <!-- جدول التنبيهات -->
            <div class="notifications-table">
                <div class="table-responsive">
                    <table class="table text-center align-middle">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> #</th>
                                <th><i class="fas fa-heading"></i> العنوان</th>
                                <th><i class="fas fa-align-left"></i> الوصف</th>
                                <th><i class="fas fa-calendar-alt"></i> من تاريخ</th>
                                <th><i class="fas fa-calendar-check"></i> إلى تاريخ</th>
                                <th><i class="fas fa-tasks"></i> الحالة</th>
                                <?php if ($isAdmin): ?><th><i class="fas fa-cogs"></i> الإجراءات</th><?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($notifications)): ?>
                                <?php foreach ($notifications as $index => $note): ?>
                                    <tr class="task-row <?= $note['row_class'] ?>">
                                        <td><strong><?= ($currentPage - 1) * $limit + $index + 1 ?></strong></td>
                                        <td><strong><?= htmlspecialchars($note['Title']) ?></strong></td>
                                        <td style="text-align: right; max-width: 300px;">
                                            <?= nl2br(htmlspecialchars($note['Message'])) ?>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar-alt" style="color: #007bff;"></i>
                                            <?= date('d/m/Y', strtotime($note['NotifyDate'])) ?>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar-check" style="color: #007bff;"></i>
                                            <?= date('d/m/Y', strtotime($note['ToDate'])) ?>
                                        </td>
                                        <td>
                                            <span class="status-badge <?= $note['status_class'] ?>">
                                                <i class="<?= $note['status_icon'] ?>"></i>
                                                <?= $note['Status'] ?? 'غير محددة' ?>
                                            </span>
                                            <?php if ($note['days_remaining']): ?>
                                                <br><small class="text-muted"><?= $note['days_remaining'] ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <?php if ($isAdmin): ?>
                                        <td>
                                            <div class="action-buttons">
                                                <form method="POST" style="width: 100%;">
                                                    <input type="hidden" name="id" value="<?= $note['ID'] ?>">
                                                    <select name="status" class="form-select" required>
                                                        <option value="تم" <?= $note['Status'] === 'تم' ? 'selected' : '' ?>>
                                                            ✅ تم إنجازها
                                                        </option>
                                                        <option value="تحت التنفيذ" <?= $note['Status'] === 'تحت التنفيذ' ? 'selected' : '' ?>>
                                                            🔄 تحت التنفيذ
                                                        </option>
                                                        <option value="لم تنفذ" <?= $note['Status'] === 'لم تنفذ' ? 'selected' : '' ?>>
                                                            ❌ لم تنفذ بعد
                                                        </option>
                                                    </select>
                                                    <button type="submit" name="update_status" class="btn btn-update-status">
                                                        <i class="fas fa-save"></i> تحديث الحالة
                                                    </button>
                                                    <?php if ($note['Status'] === 'لم تنفذ'): ?>
                                                        <button name="reschedule" value="<?= $note['ID'] ?>" class="btn btn-reschedule">
                                                            <i class="fas fa-calendar-plus"></i> ترحيل للغد
                                                        </button>
                                                    <?php endif; ?>
                                                    <a href="edit_notification.php?id=<?= $note['ID'] ?>" class="btn btn-edit">
                                                        <i class="fas fa-edit"></i> تعديل
                                                    </a>
                                                    <a href="?delete=<?= $note['ID'] ?>" class="btn btn-delete"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذه المهمة؟');">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a>
                                                </form>
                                            </div>
                                        </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="<?= $isAdmin ? 7 : 6 ?>" style="padding: 40px; color: #6c757d;">
                                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                        <h5>لا توجد مهام مطابقة للفترة المحددة</h5>
                                        <p class="mb-0">جرب تغيير الشهر أو إزالة فلتر الحالة</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- نظام الصفحات -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination-container mt-4">
                    <nav aria-label="صفحات المهام">
                        <ul class="pagination justify-content-center">
                            <!-- الصفحة السابقة -->
                            <?php if ($currentPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage - 1])) ?>">
                                        <i class="fas fa-chevron-right"></i> السابق
                                    </a>
                                </li>
                            <?php endif; ?>

                            <!-- أرقام الصفحات -->
                            <?php
                            $startPage = max(1, $currentPage - 2);
                            $endPage = min($totalPages, $currentPage + 2);

                            if ($startPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>">1</a>
                                </li>
                                <?php if ($startPage > 2): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($endPage < $totalPages): ?>
                                <?php if ($endPage < $totalPages - 1): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>"><?= $totalPages ?></a>
                                </li>
                            <?php endif; ?>

                            <!-- الصفحة التالية -->
                            <?php if ($currentPage < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage + 1])) ?>">
                                        التالي <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>

                    <!-- معلومات الصفحة -->
                    <div class="pagination-info text-center mt-2">
                        <small class="text-muted">
                            عرض <?= ($currentPage - 1) * $limit + 1 ?> - <?= min($currentPage * $limit, $totalTasks) ?> من أصل <?= $totalTasks ?> مهمة
                            (الصفحة <?= $currentPage ?> من <?= $totalPages ?>)
                        </small>
                    </div>
                </div>
                <?php endif; ?>
            </div>

        </div>
    </div>
</div>

<!-- النافذة المنبثقة لتفاصيل المهمة -->
<div class="modal-overlay" id="taskModal">
    <div class="modal-content">
        <div class="modal-header d-flex justify-content-between align-items-center mb-3">
            <h5 class="modal-title"><i class="fas fa-tasks"></i> تفاصيل المهمة</h5>
            <button type="button" class="btn-close" onclick="closeTaskModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="taskModalBody">
            <!-- سيتم تحميل المحتوى هنا -->
        </div>
    </div>
</div>

<!-- زر العودة لأعلى -->
<button onclick="scrollToTop()" id="scrollBtn" title="العودة لأعلى" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000; background: #007bff; color: white; border: none; border-radius: 50%; width: 50px; height: 50px; display: none; cursor: pointer; box-shadow: 0 2px 10px rgba(0,0,0,0.3); transition: all 0.3s ease;">
    <i class="fas fa-arrow-up"></i>
</button>

<script>
// ===== سكريبت محسن لصفحة إدارة المهام مع تحسينات الأداء =====

// إخفاء شاشة التحميل عند اكتمال التحميل
document.addEventListener('DOMContentLoaded', function() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 300);
        }, 500);
    }
});

// تحسين الأداء - تجميع العمليات
const performanceOptimizer = {
    // تجميع تحديثات DOM
    batchDOMUpdates: function(updates) {
        requestAnimationFrame(() => {
            updates.forEach(update => update());
        });
    },

    // تأخير العمليات الثقيلة
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // تحسين التمرير
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    // تحميل تدريجي للصور
    lazyLoadImages: function() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    },

    // تحسين الجداول الكبيرة
    virtualizeTable: function(tableSelector, rowHeight = 50) {
        const table = document.querySelector(tableSelector);
        if (!table) return;

        const rows = table.querySelectorAll('tbody tr');
        if (rows.length < 100) return; // لا حاجة للتحسين للجداول الصغيرة

        const container = table.parentElement;
        const visibleRows = Math.ceil(container.clientHeight / rowHeight) + 5;
        let startIndex = 0;

        const updateVisibleRows = performanceOptimizer.throttle(() => {
            const scrollTop = container.scrollTop;
            startIndex = Math.floor(scrollTop / rowHeight);

            rows.forEach((row, index) => {
                if (index >= startIndex && index < startIndex + visibleRows) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }, 16);

        container.addEventListener('scroll', updateVisibleRows);
        updateVisibleRows();
    }
};

// تحسين إدارة الذاكرة
const memoryManager = {
    // تنظيف المستمعين غير المستخدمين
    cleanupEventListeners: function() {
        // إزالة المستمعين من العناصر المحذوفة
        const elements = document.querySelectorAll('[data-cleanup]');
        elements.forEach(element => {
            if (!document.contains(element)) {
                element.removeEventListener('click', element._clickHandler);
                element.removeEventListener('change', element._changeHandler);
            }
        });
    },

    // تحسين استخدام الذاكرة للبيانات الكبيرة
    optimizeDataStructures: function() {
        // تحويل NodeList إلى Array فقط عند الحاجة
        // استخدام WeakMap للبيانات المؤقتة
        if (!window.taskDataCache) {
            window.taskDataCache = new WeakMap();
        }
    }
};

// تحسين الشبكة
const networkOptimizer = {
    // تجميع طلبات API
    batchAPIRequests: function(requests) {
        return Promise.all(requests.map(request =>
            fetch(request.url, request.options)
                .then(response => response.json())
                .catch(error => ({ error: error.message }))
        ));
    },

    // تخزين مؤقت للطلبات
    cache: new Map(),

    cachedFetch: function(url, options = {}) {
        const cacheKey = url + JSON.stringify(options);

        if (this.cache.has(cacheKey)) {
            return Promise.resolve(this.cache.get(cacheKey));
        }

        return fetch(url, options)
            .then(response => response.json())
            .then(data => {
                this.cache.set(cacheKey, data);
                // تنظيف التخزين المؤقت بعد 5 دقائق
                setTimeout(() => this.cache.delete(cacheKey), 300000);
                return data;
            });
    }
};

// إظهار زر العودة لأعلى عند التمرير
window.onscroll = function() {
    let btn = document.getElementById("scrollBtn");
    if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
        btn.style.display = "block";
        btn.style.opacity = "1";
    } else {
        btn.style.opacity = "0";
        setTimeout(() => {
            if (document.body.scrollTop <= 200 && document.documentElement.scrollTop <= 200) {
                btn.style.display = "none";
            }
        }, 300);
    }
};

// العودة لأعلى الصفحة
function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.querySelector('select[name="status"]').value = '';
    document.querySelector('input[name="date_from"]').value = '';
    document.querySelector('input[name="date_to"]').value = '';
    document.getElementById('searchForm').submit();
}

// تصدير المهام
function exportTasks() {
    const currentUrl = new URL(window.location);
    const exportUrl = new URL('export_tasks.php', window.location.origin + '/e-finance/');

    // نسخ جميع المعاملات الحالية
    currentUrl.searchParams.forEach((value, key) => {
        exportUrl.searchParams.set(key, value);
    });

    exportUrl.searchParams.set('type', 'excel');
    window.open(exportUrl.toString(), '_blank');
}

// تصدير CSV
function exportCSV() {
    const currentUrl = new URL(window.location);
    const exportUrl = new URL('export_tasks.php', window.location.origin + '/e-finance/');

    currentUrl.searchParams.forEach((value, key) => {
        exportUrl.searchParams.set(key, value);
    });

    exportUrl.searchParams.set('type', 'csv');
    window.open(exportUrl.toString(), '_blank');
}

// تصدير PDF
function exportPDF() {
    const currentUrl = new URL(window.location);
    const exportUrl = new URL('export_tasks.php', window.location.origin + '/e-finance/');

    currentUrl.searchParams.forEach((value, key) => {
        exportUrl.searchParams.set(key, value);
    });

    exportUrl.searchParams.set('type', 'pdf');
    window.open(exportUrl.toString(), '_blank');
}

// طباعة التقرير المحسنة
function printReport() {
    // إنشاء نافذة طباعة مخصصة
    const printWindow = window.open('', '_blank');
    const currentUrl = new URL(window.location);

    // جمع البيانات المرئية
    const visibleRows = document.querySelectorAll('.task-row:not([style*="display: none"])');
    const stats = {
        total: visibleRows.length,
        completed: document.querySelectorAll('.task-row:not([style*="display: none"]) .status-completed').length,
        inProgress: document.querySelectorAll('.task-row:not([style*="display: none"]) .status-in-progress').length,
        pending: document.querySelectorAll('.task-row:not([style*="display: none"]) .status-not-started').length
    };

    // إنشاء HTML للطباعة
    let printHTML = `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>تقرير المهام - ${new Date().toLocaleDateString('ar-SA')}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
            .filters { background-color: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 12px; }
            th, td { border: 1px solid #ddd; padding: 6px; text-align: center; }
            th { background-color: #007bff; color: white; font-weight: bold; }
            tr:nth-child(even) { background-color: #f2f2f2; }
            .stats { background-color: #e9ecef; padding: 15px; border-radius: 5px; display: flex; justify-content: space-around; }
            @media print { body { margin: 0; } }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>تقرير المهام</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA')}</p>
        </div>

        <div class="filters">
            <h3>معلومات التقرير:</h3>
            <p><strong>عدد المهام المعروضة:</strong> ${stats.total}</p>
            <p><strong>الفلاتر المطبقة:</strong> ${getAppliedFilters()}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>العنوان</th>
                    <th>الأولوية</th>
                    <th>التصنيف</th>
                    <th>المسؤول</th>
                    <th>من تاريخ</th>
                    <th>إلى تاريخ</th>
                    <th>الحالة</th>
                    <th>التقدم</th>
                </tr>
            </thead>
            <tbody>`;

    // إضافة البيانات
    visibleRows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 9) {
            printHTML += `
                <tr>
                    <td>${index + 1}</td>
                    <td style="text-align: right;">${cells[1].textContent.trim()}</td>
                    <td>${cells[2].textContent.trim()}</td>
                    <td>${cells[3].textContent.trim()}</td>
                    <td>${cells[4].textContent.trim()}</td>
                    <td>${cells[5].querySelector('.date-badge') ? cells[5].querySelector('.date-badge').textContent.trim() : ''}</td>
                    <td>${cells[5].querySelectorAll('.date-badge')[1] ? cells[5].querySelectorAll('.date-badge')[1].textContent.trim() : ''}</td>
                    <td>${cells[6].textContent.trim()}</td>
                    <td>${cells[7].textContent.trim()}</td>
                </tr>`;
        }
    });

    printHTML += `
            </tbody>
        </table>

        <div class="stats">
            <div><strong>مكتملة:</strong><br>${stats.completed}</div>
            <div><strong>قيد التنفيذ:</strong><br>${stats.inProgress}</div>
            <div><strong>في الانتظار:</strong><br>${stats.pending}</div>
            <div><strong>معدل الإنجاز:</strong><br>${stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}%</div>
        </div>

        <script>
            window.onload = function() {
                window.print();
                window.onafterprint = function() {
                    window.close();
                };
            };
        </script>
    </body>
    </html>`;

    printWindow.document.write(printHTML);
    printWindow.document.close();
}

// إرسال التقرير بالبريد الإلكتروني
function emailReport() {
    const subject = encodeURIComponent('تقرير المهام - ' + new Date().toLocaleDateString('ar-SA'));
    const body = encodeURIComponent(`
مرفق تقرير المهام للمراجعة.

تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}
عدد المهام: ${document.querySelectorAll('.task-row:not([style*="display: none"])').length}

يرجى مراجعة التقرير المرفق.

مع تحياتي
    `);

    // فتح عميل البريد الإلكتروني
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
}

// دالة مساعدة للحصول على الفلاتر المطبقة
function getAppliedFilters() {
    const filters = [];

    const searchValue = document.querySelector('input[name="search"]').value;
    if (searchValue) filters.push(`البحث: ${searchValue}`);

    const statusValue = document.querySelector('select[name="status"]').value;
    if (statusValue) filters.push(`الحالة: ${statusValue}`);

    const priorityValue = document.querySelector('select[name="priority"]').value;
    if (priorityValue) filters.push(`الأولوية: ${priorityValue}`);

    const categoryValue = document.querySelector('select[name="category"]').value;
    if (categoryValue) filters.push(`التصنيف: ${categoryValue}`);

    const dateFromValue = document.querySelector('input[name="date_from"]').value;
    if (dateFromValue) filters.push(`من تاريخ: ${dateFromValue}`);

    const dateToValue = document.querySelector('input[name="date_to"]').value;
    if (dateToValue) filters.push(`إلى تاريخ: ${dateToValue}`);

    return filters.length > 0 ? filters.join(' | ') : 'لا توجد فلاتر مطبقة';
}

// عرض تفاصيل المهمة
function showTaskDetails(taskId) {
    const modal = document.getElementById('taskModal');
    const modalBody = document.getElementById('taskModalBody');

    // إظهار النافذة المنبثقة
    modal.style.display = 'flex';
    modalBody.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    // جلب تفاصيل المهمة
    fetch(`get_task_details.php?id=${taskId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                modalBody.innerHTML = data.html;
            } else {
                modalBody.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            modalBody.innerHTML = '<div class="alert alert-danger">حدث خطأ في الاتصال</div>';
        });
}

// إغلاق النافذة المنبثقة
function closeTaskModal() {
    const modal = document.getElementById('taskModal');
    modal.style.display = 'none';
}

// إغلاق النافذة عند النقر خارجها
document.getElementById('taskModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTaskModal();
    }
});

// إضافة تعليق
function addComment(taskId) {
    const commentText = document.getElementById('newComment').value.trim();

    if (!commentText) {
        alert('يرجى كتابة التعليق');
        return;
    }

    const formData = new FormData();
    formData.append('task_id', taskId);
    formData.append('comment', commentText);

    fetch('add_comment.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إعادة تحميل تفاصيل المهمة
            showTaskDetails(taskId);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// البحث الفوري المحسن مع تحسينات الأداء
const optimizedSearch = performanceOptimizer.debounce(function(searchTerm) {
    const rows = document.querySelectorAll('.task-row');
    let visibleCount = 0;
    const updates = [];

    // تجميع التحديثات لتحسين الأداء
    rows.forEach(row => {
        const title = row.querySelector('.task-title').textContent.toLowerCase();
        const description = row.querySelector('.task-description') ?
                          row.querySelector('.task-description').textContent.toLowerCase() : '';

        const isVisible = searchTerm === '' || title.includes(searchTerm) || description.includes(searchTerm);

        updates.push(() => {
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) visibleCount++;
        });
    });

    // تطبيق التحديثات في دفعة واحدة
    performanceOptimizer.batchDOMUpdates(updates);

    // عرض عدد النتائج
    requestAnimationFrame(() => updateResultsCount(visibleCount));
}, 300);

document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    optimizedSearch(searchTerm);
});

// تحديث عدد النتائج
function updateResultsCount(count) {
    let countElement = document.getElementById('resultsCount');
    if (!countElement) {
        countElement = document.createElement('div');
        countElement.id = 'resultsCount';
        countElement.className = 'alert alert-info mt-2';
        document.querySelector('.search-filter-section').appendChild(countElement);
    }
    countElement.innerHTML = `<i class="fas fa-info-circle"></i> عدد النتائج: ${count} مهمة`;
}

// البحث السريع
function quickFilter(type) {
    const form = document.getElementById('searchForm');
    const today = new Date().toISOString().split('T')[0];
    const weekFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // مسح الفلاتر الحالية
    clearFilters();

    switch(type) {
        case 'overdue':
            form.querySelector('select[name="status"]').value = 'لم تنفذ';
            form.querySelector('input[name="date_to"]').value = today;
            break;
        case 'today':
            form.querySelector('input[name="date_from"]').value = today;
            form.querySelector('input[name="date_to"]').value = today;
            break;
        case 'this_week':
            form.querySelector('input[name="date_from"]').value = today;
            form.querySelector('input[name="date_to"]').value = weekFromNow;
            break;
        case 'completed':
            form.querySelector('select[name="status"]').value = 'تم';
            break;
        case 'high_priority':
            form.querySelector('select[name="priority"]').value = 'عالية';
            break;
        case 'my_tasks':
            // يحتاج معرف المستخدم الحالي
            const currentUserId = '<?= $_SESSION['user_id'] ?>';
            form.querySelector('select[name="assigned_to"]').value = currentUserId;
            break;
    }

    form.submit();
}

// حفظ الفلاتر الحالية
function saveCurrentFilters() {
    const filterName = prompt('أدخل اسم للفلاتر المحفوظة:');
    if (!filterName) return;

    const currentFilters = {
        search: document.querySelector('input[name="search"]').value,
        status: document.querySelector('select[name="status"]').value,
        priority: document.querySelector('select[name="priority"]').value,
        category: document.querySelector('select[name="category"]').value,
        assigned_to: document.querySelector('select[name="assigned_to"]').value,
        progress: document.querySelector('select[name="progress"]').value,
        date_from: document.querySelector('input[name="date_from"]').value,
        date_to: document.querySelector('input[name="date_to"]').value,
        created_by: document.querySelector('select[name="created_by"]').value,
        sort_by: document.querySelector('select[name="sort_by"]').value,
        sort_order: document.querySelector('select[name="sort_order"]').value
    };

    // حفظ في localStorage
    let savedFilters = JSON.parse(localStorage.getItem('taskFilters') || '{}');
    savedFilters[filterName] = currentFilters;
    localStorage.setItem('taskFilters', JSON.stringify(savedFilters));

    // تحديث القائمة
    loadSavedFilters();

    alert('تم حفظ الفلاتر بنجاح!');
}

// تحميل الفلاتر المحفوظة
function loadSavedFilters() {
    const savedFilters = JSON.parse(localStorage.getItem('taskFilters') || '{}');
    const menu = document.getElementById('savedFiltersMenu');

    menu.innerHTML = '';

    if (Object.keys(savedFilters).length === 0) {
        menu.innerHTML = '<li><span class="dropdown-item-text text-muted">لا توجد فلاتر محفوظة</span></li>';
        return;
    }

    Object.keys(savedFilters).forEach(name => {
        const li = document.createElement('li');
        li.innerHTML = `
            <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" onclick="applySavedFilter('${name}')">
                <span><i class="fas fa-filter"></i> ${name}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); deleteSavedFilter('${name}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </a>
        `;
        menu.appendChild(li);
    });
}

// تطبيق فلتر محفوظ
function applySavedFilter(name) {
    const savedFilters = JSON.parse(localStorage.getItem('taskFilters') || '{}');
    const filters = savedFilters[name];

    if (!filters) return;

    // تطبيق الفلاتر
    Object.keys(filters).forEach(key => {
        const element = document.querySelector(`[name="${key}"]`);
        if (element) {
            element.value = filters[key];
        }
    });

    // إرسال النموذج
    document.getElementById('searchForm').submit();
}

// حذف فلتر محفوظ
function deleteSavedFilter(name) {
    if (!confirm(`هل تريد حذف الفلتر "${name}"؟`)) return;

    let savedFilters = JSON.parse(localStorage.getItem('taskFilters') || '{}');
    delete savedFilters[name];
    localStorage.setItem('taskFilters', JSON.stringify(savedFilters));

    loadSavedFilters();
}

// تأكيد الحذف مع تحسين الرسالة
document.querySelectorAll('.btn-delete').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
        if (!confirm('⚠️ تحذير!\n\nهل أنت متأكد من حذف هذه المهمة؟\nلا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
        }
    });
});

// تحسين تجربة المستخدم عند تحديث الحالة
document.querySelectorAll('.btn-update-status').forEach(function(btn) {
    btn.addEventListener('click', function() {
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
        this.disabled = true;

        // إعادة تفعيل الزر في حالة فشل الإرسال
        setTimeout(() => {
            this.innerHTML = originalText;
            this.disabled = false;
        }, 10000);
    });
});

// فلترة حسب الحالة
function filterByStatus(status) {
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    url.searchParams.delete('search');
    window.location.href = url.toString();
}

// تحسين تفاعل الإحصائيات مع تأثيرات بصرية
document.querySelectorAll('.stat-card').forEach(function(card) {
    // إضافة تأثير النقر
    card.addEventListener('mousedown', function() {
        this.style.transform = 'scale(0.95)';
    });

    card.addEventListener('mouseup', function() {
        this.style.transform = 'scale(1)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
    });
});

// تحديث الإحصائيات في الوقت الفعلي
function updateStatsRealTime() {
    fetch('get_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الأرقام
                document.querySelector('.stat-card.completed .stat-number').textContent = data.stats.completed;
                document.querySelector('.stat-card.in-progress .stat-number').textContent = data.stats.in_progress;
                document.querySelector('.stat-card.pending .stat-number').textContent = data.stats.pending;
                document.querySelector('.stat-card.overdue .stat-number').textContent = data.stats.overdue;

                // تحديث النسب المئوية
                const completionRate = data.stats.total > 0 ? ((data.stats.completed / data.stats.total) * 100).toFixed(1) : 0;
                document.querySelector('.stat-card.completed .stat-percentage').textContent = completionRate + '% من الإجمالي';
            }
        })
        .catch(error => console.error('خطأ في تحديث الإحصائيات:', error));
}

// تحديث الإحصائيات كل 5 دقائق
setInterval(updateStatsRealTime, 300000);

// إضافة تأثيرات تحريك للإحصائيات عند التحميل
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');

    statNumbers.forEach(function(element) {
        const finalValue = parseInt(element.textContent);
        let currentValue = 0;
        const increment = finalValue / 50; // 50 خطوة للوصول للرقم النهائي

        const timer = setInterval(function() {
            currentValue += increment;
            if (currentValue >= finalValue) {
                element.textContent = finalValue;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(currentValue);
            }
        }, 30);
    });
}

// تشغيل الأنيميشن عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(animateStats, 500);
});

// تحسين تجربة النماذج
document.querySelectorAll('input, select, textarea').forEach(function(element) {
    element.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });

    element.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });
});

// إضافة تأثيرات صوتية (اختيارية)
function playClickSound() {
    // يمكن إضافة صوت نقرة هنا
    // const audio = new Audio('click.mp3');
    // audio.play().catch(() => {});
}

// تحسين إمكانية الوصول
document.addEventListener('keydown', function(e) {
    // اختصار لوحة المفاتيح للبحث
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('searchInput').focus();
    }

    // اختصار للعودة لأعلى
    if (e.key === 'Home' && e.ctrlKey) {
        e.preventDefault();
        scrollToTop();
    }
});

// تحديث الوقت الحقيقي للمدة المتبقية
function updateRemainingTime() {
    const today = new Date();
    document.querySelectorAll('.task-row').forEach(row => {
        const taskId = row.dataset.taskId;
        // يمكن إضافة تحديث للوقت المتبقي هنا
    });
}

// تحديث كل دقيقة
setInterval(updateRemainingTime, 60000);

// تحسين التحميل مع تحسينات الأداء
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء شاشة التحميل
    const loadingOverlay = document.getElementById('loadingOverlay');

    // تأخير إخفاء شاشة التحميل لضمان تحميل كامل
    setTimeout(() => {
        loadingOverlay.classList.add('hidden');
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 300);
    }, 500);

    // تطبيق تحسينات الأداء
    memoryManager.optimizeDataStructures();
    performanceOptimizer.lazyLoadImages();

    // تحسين الجداول الكبيرة
    const table = document.querySelector('.notifications-table table');
    if (table && table.rows.length > 100) {
        performanceOptimizer.virtualizeTable('.notifications-table');
        console.log('تم تفعيل التحميل التدريجي للجدول الكبير');
    }

    // تحميل الفلاتر المحفوظة
    loadSavedFilters();

    // إضافة اختصارات لوحة المفاتيح للبحث السريع
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey) {
            switch(e.key) {
                case 'T': // Ctrl+Shift+T للمهام اليوم
                    e.preventDefault();
                    quickFilter('today');
                    break;
                case 'W': // Ctrl+Shift+W لمهام الأسبوع
                    e.preventDefault();
                    quickFilter('this_week');
                    break;
                case 'H': // Ctrl+Shift+H للأولوية العالية
                    e.preventDefault();
                    quickFilter('high_priority');
                    break;
                case 'M': // Ctrl+Shift+M لمهامي
                    e.preventDefault();
                    quickFilter('my_tasks');
                    break;
            }
        }
    });

    // عرض عدد النتائج الأولي
    const totalRows = document.querySelectorAll('.task-row').length;
    updateResultsCount(totalRows);

    // تشغيل الأنيميشن للإحصائيات
    setTimeout(animateStats, 800);

    // تنظيف دوري للذاكرة
    setInterval(() => {
        memoryManager.cleanupEventListeners();
        // تنظيف التخزين المؤقت القديم
        if (networkOptimizer.cache.size > 50) {
            networkOptimizer.cache.clear();
        }
    }, 300000); // كل 5 دقائق

    // مراقبة الأداء
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('وقت التحميل:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }

    // تحسين التمرير
    const scrollableElements = document.querySelectorAll('.table-responsive, .modal-content');
    scrollableElements.forEach(element => {
        element.classList.add('smooth-scroll');
    });

    // إعداد البحث السريع المحسن
    setupQuickSearch();
});

// البحث السريع المحسن مع تحميل تدريجي
function setupQuickSearch() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;

    const quickSearch = performanceOptimizer.debounce(function(query) {
        if (query.length < 2) {
            // إظهار جميع الصفوف إذا كان البحث فارغاً
            const rows = document.querySelectorAll('.task-row');
            rows.forEach(row => row.style.display = '');
            hideSearchResults();
            return;
        }

        // البحث في الجدول الحالي أولاً (للاستجابة السريعة)
        const rows = document.querySelectorAll('.task-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const title = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const description = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

            if (title.includes(query.toLowerCase()) || description.includes(query.toLowerCase())) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // إظهار عدد النتائج
        updateSearchResults(visibleCount);
    }, 300);

    searchInput.addEventListener('input', function() {
        quickSearch(this.value);
    });
}

// تحديث عدد نتائج البحث
function updateSearchResults(count) {
    let resultDiv = document.getElementById('searchResults');
    if (!resultDiv) {
        resultDiv = document.createElement('div');
        resultDiv.id = 'searchResults';
        resultDiv.className = 'alert alert-info mt-2';
        resultDiv.style.display = 'none';
        const searchSection = document.querySelector('.search-filter-section');
        if (searchSection) {
            searchSection.appendChild(resultDiv);
        }
    }

    if (count === 0) {
        resultDiv.innerHTML = '<i class="fas fa-search"></i> لم يتم العثور على نتائج';
        resultDiv.className = 'alert alert-warning mt-2';
    } else {
        resultDiv.innerHTML = `<i class="fas fa-check"></i> تم العثور على ${count} نتيجة`;
        resultDiv.className = 'alert alert-success mt-2';
    }

    resultDiv.style.display = 'block';
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const resultDiv = document.getElementById('searchResults');
    if (resultDiv) {
        resultDiv.style.display = 'none';
    }
}
</script>

<!-- تحميل مؤجل للموارد -->
<script>
// تحميل Bootstrap بشكل مؤجل
const loadBootstrap = () => {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
    script.async = true;
    document.head.appendChild(script);
};

// تحميل الموارد بعد تحميل الصفحة
window.addEventListener('load', () => {
    loadBootstrap();

    // تحميل ملف JavaScript الإضافي إذا كان موجوداً
    if (document.querySelector('script[src="notifications-script.js"]') === null) {
        const script = document.createElement('script');
        script.src = 'notifications-script.js';
        script.async = true;
        script.onerror = () => console.log('ملف notifications-script.js غير موجود');
        document.head.appendChild(script);
    }
});
</script>

<!-- Service Worker للتخزين المؤقت -->
<script>
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => console.log('SW registered'))
            .catch(error => console.log('SW registration failed'));
    });
}
</script>
</body>
</html>
